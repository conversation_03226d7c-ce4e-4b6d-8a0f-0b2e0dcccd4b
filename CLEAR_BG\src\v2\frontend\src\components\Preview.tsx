import { useEffect, useState, useCallback } from "react";
import Button from "./Button";
import Download from "./icons/DownloadIcon";
import TrashIcon from "./icons/TrashIcon";

const Preview = ({
  isGenerating,
  image,
  toggleImage,
  onDelete,
  onDownload,
  originalImage,
}: {
  isGenerating: boolean;
  image: string;
  originalImage: string;
  toggleImage: () => void;
  onDownload: () => void;
  onDelete: () => void;
}) => {
  const [imageWidth, setImageWidth] = useState(0);
  const [imageHeight, setImageHeight] = useState(0);

  const isMobile = window.innerWidth <= 768;

  const maxWidth = isMobile ? 320 : 450;
  const maxHeight = isMobile ? 320 : 450;

  const resizeImage = useCallback(
    async (imageWidth: number, imageHeight: number) => {
      const ratio = imageWidth / imageHeight;

      if (ratio > 1) {
        // landscape
        const newWidth = maxWidth;
        const newHeight = maxWidth / ratio;
        setImageWidth(newWidth);
        setImageHeight(newHeight);
      } else {
        // portrait
        const newWidth = maxHeight * ratio;
        const newHeight = maxHeight;
        setImageWidth(newWidth);
        setImageHeight(newHeight);
      }
    },
    [maxWidth, maxHeight],
  );

  const img = new Image();
  img.src = originalImage;
  img.onload = () => {
    resizeImage(img.width, img.height);
  };

  // resize observer
  useEffect(() => {
    resizeImage(img.width, img.height);
    const resizeObserver = new ResizeObserver(() => {
      resizeImage(img.width, img.height);
    });

    resizeObserver.observe(document.body);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div className="preview flex h-full flex-col justify-between">
      {/* <div className="scroll-1 relative max-h-[599px] overflow-auto"></div> */}
      <div className="inline-flex flex-col items-center justify-start gap-[16px] self-stretch">
        <div className="inline-flex items-center justify-start self-stretch py-2.5">
          <div className="flex flex-1 items-center justify-start gap-0.5">
            <div className="text-black-100 justify-start font-['Inter'] text-xs leading-none font-semibold">
              Result
            </div>
          </div>
        </div>
        <div className="relative flex w-[320px] max-w-full items-center justify-center overflow-hidden rounded-xl md:w-[450px]">
          <img
            src="./images/loading-image.gif"
            loading="lazy"
            style={{ width: imageWidth, height: imageHeight }}
            className={`${isGenerating ? "block" : "hidden"} max-w-full rounded-xl border-1 border-[#E4E4E7] dark:brightness-40`}
          />

          {!isGenerating && (
            <div
              className="relative flex items-center justify-center overflow-hidden rounded-lg"
              style={{ width: imageWidth, height: imageHeight }}
            >
              <img
                className="h-full w-full rounded-xl object-cover"
                src={image}
              />
              <div
                className="bg-gradient-black absolute inset-0 flex w-full items-start justify-end p-[10px] opacity-0 group hover:opacity-100"
                onClick={toggleImage}
              >
                <Button
                  variant="secondary"
                  size="sm"
                  className="mr-[8px] w-[30px] rounded-md px-0 group-hover:flex hidden"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                  }}
                >
                  <TrashIcon size={20} />
                </Button>

                <Button
                  variant="secondary"
                  size="sm"
                  className="w-[30px] rounded-md px-0 group-hover:flex hidden"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownload();
                  }}
                >
                  <Download size={18} />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="dark:bg-black-100 inline-flex h-[64px] w-full max-w-full items-center justify-end bg-white pt-[8px]">
        <Button
          variant="outline"
          size="lg"
          onClick={() => onDownload()}
          className="w-full hover:bg-[#3073D5] hover:text-[#ffffff] md:w-auto"
        >
          <Download size={24} className="mr-[10px]" />
          Download
        </Button>
      </div>
    </div>
  );
};

export default Preview;
