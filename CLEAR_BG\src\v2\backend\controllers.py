"""FastAPI backend server for serving API and React frontend."""

from io import BytesIO
from fastapi import File, UploadFile, APIRouter, HTTPException, Request, Depends
from fastapi.responses import StreamingResponse
from rembg import remove
import httpx
from utils.initializer import get_total_apps
from limited_usage import get_usage, get_limit
from utils.cookie_utils import set_cookie

router = APIRouter()


async def is_limited(request: Request):
    total_apps = await get_total_apps()
    tried_usage = get_usage()
    # request.cookies.get("token")
    limit = get_limit()

    if tried_usage >= limit:
        raise HTTPException(
            status_code=401,
            detail={
                "error": "limited_usage",
                "message": (
                    f"Switch now to PRO and get access to <span>{total_apps}</span> "
                    "different creativity and productivity AI tools."
                ),
            },
        )


def validate_image(file: UploadFile = File(...)) -> UploadFile:

    set_cookie("test123", "123", max_age=3600)

    allowed_types = {"image/png", "image/jpeg", "image/jpg", "image/jfif", "image/gif"}
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail={
                "error": "invalid_file_type",
                "message": "Unsupported file type. Please upload a PNG, JPEG, JPG, JFIF, or GIF file.",
            },
        )
    return file


@router.post("/remove-bg")
async def remove_bg(
    request: Request, file: UploadFile = Depends(validate_image), _=Depends(is_limited)
):
    input_data = await file.read()
    output_data = remove(input_data)
    response = StreamingResponse(BytesIO(output_data), media_type="image/png")
    response.headers["Content-Length"] = str(len(output_data))
    return response
