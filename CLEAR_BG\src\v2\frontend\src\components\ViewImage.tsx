import { useEffect, useState } from "react";
import { AiOutlineClose } from "react-icons/ai";
const ViewImage = ({
  src,
  isOpen,
  onClose,
}: {
  src: string;
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [imageWidth, setImageWidth] = useState(0);
  const [imageHeight, setImageHeight] = useState(0);

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  const resizeImage = () => {
    const isMobile = window.innerWidth <= 768;
    const maxWidth = isMobile ? 320 : 480;
    const maxHeight = isMobile ? 320 : 480;

    const img = new Image();
    img.src = src;

    img.onload = () => {
      const imageWidth = img.width;
      const imageHeight = img.height;
      const ratio = imageWidth / imageHeight;

      if (ratio > 1) {
        // landscape
        const newWidth = maxWidth;
        const newHeight = maxWidth / ratio;
        setImageWidth(newWidth);
        setImageHeight(newHeight);
      } else {
        // portrait
        const newWidth = maxHeight * ratio;
        const newHeight = maxHeight;
        setImageWidth(newWidth);
        setImageHeight(newHeight);
      }
    };
  };
  resizeImage();

  // resize observer
  useEffect(() => {
    resizeImage();
    const resizeObserver = new ResizeObserver(() => {
      resizeImage();
    });

    resizeObserver.observe(document.body);

    return () => {
      resizeObserver.disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!isOpen) return null;

  return (
    <div
      className="fixed top-0 left-0 flex h-full w-full items-center justify-center bg-[#000000A6]"
      onClick={handleOverlayClick}
    >
      <div className="relative flex flex-col items-start justify-center rounded-[11px] border border-[#E4E4E7] bg-white p-[25px] md:px-[50px] md:py-[43px]">
        {/* image */}
        <img
          src={src}
          alt=""
          className="rounded-lg object-cover"
          style={{ width: imageWidth, height: imageHeight }}
        />

        {/* close icon */}
        <div className="absolute top-[10px] right-[10px] cursor-pointer md:top-[23px] md:right-[23px]">
          <AiOutlineClose
            onClick={onClose}
            className="text-[16px] text-black/30 md:text-[24px]"
          />
        </div>
      </div>
    </div>
  );
};

export default ViewImage;
