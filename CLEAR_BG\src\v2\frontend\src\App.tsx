import { useState } from "react";

import Header from "./components/Header.tsx";
import Brand from "./components/Brand.tsx";
import Button from "./components/Button.tsx";
import { GrPowerReset } from "react-icons/gr";
import { FaWandMagicSparkles } from "react-icons/fa6";
import ImagePlaceholder from "./components/ImagePlaceholder.tsx";
import Preview from "./components/Preview.tsx";
import ViewImage from "./components/ViewImage.tsx";

import DragNDrop from "./components/DragNDrop.tsx";
import { ToastContainer, Bounce, toast } from "react-toastify";

import LimitedUsageModal from "./components/LimitedUsageModal";
import PricingPopup from "./components/PricingPopup.tsx";

function App() {
  const [file, setFile] = useState<File[]>([]);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [image, setImage] = useState<string>("");
  const [originalImage, setOriginalImage] = useState<string>("");

  const [showPricing, setShowPricing] = useState(false);
  const [showLimitedUsageModal, setShowLimitedUsageModal] = useState(false);
  const [desc, setDesc] = useState("");

  const onFileChange = (file: File[]) => {
    if (file.length > 0) {
      setFile(file);

      const reader = new FileReader();
      reader.readAsDataURL(file[0]);
      reader.onload = () => {
        if (typeof reader.result === "string") {
          setOriginalImage(reader.result);
        }
      };
    }
  };

  const handleRemoveBackground = async () => {
    // console.log('click')
    //   setDesc('test');
    //   setShowLimitedUsageModal(true);

    //   return;
    if (!file[0]) return;

    setIsGenerating(true);

    const formData = new FormData();
    formData.append("file", file[0]);

    const url = import.meta.env.VITE_API_URL || "http://localhost:9010";
    try {
      const response = await fetch(url + "/api/remove-bg", {
        method: "POST",
        body: formData,
        // credentials: "include", // send cookies
      });
      console.log("response", response);

      if (response.ok) {
        const data = await response.blob();
        const imageUrl = URL.createObjectURL(data);
        setImage(imageUrl);
      } else {
        try {
          const data = await response.json();

          if (data.detail.error === "limited_usage") {
            setDesc(data.detail.message);
            setShowLimitedUsageModal(true);
          } else {
            toast.error(data.detail?.message || "Unknown error");
          }
        } catch {
          toast.error("Unexpected error occurred.");
        }
      }
    } catch (error) {
      toast.error("Unexpected error occurred." + error);
    } finally {
      setIsGenerating(false);
    }

    const TPLogicRun = window.TPLogicRun;
    if (typeof TPLogicRun === "function") {
      TPLogicRun();
    }
  };

  const resetAll = () => {
    setIsGenerating(false);
    setViewImageisOpen(false);
    setFile([]);
    setImage("");
    setOriginalImage("");
  };

  const [viewImageisOpen, setViewImageisOpen] = useState(false);

  const toggleImage = (value: boolean) => {
    setViewImageisOpen(value);
    if (value) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  };

  const onDownload = () => {
    const link = document.createElement("a");
    link.download = "image.jpg";
    link.href = image;
    link.click();
  };

  return (
    <div className="dark:bg-black-100 flex h-screen max-h-[100vh] flex-col bg-white">
      <Header setShowPricing={setShowPricing} />
      <Brand />
      <div className="md:px[40px] mx-auto mb-[32px] flex h-[695px] max-w-[1280px] flex-col gap-[33px] px-[27px] md:flex-row">
        <div className="relative flex h-full w-full flex-col overflow-hidden rounded-[17px] border border-[#E4E4E7] px-[14px] py-[16px] md:w-[492px] dark:border-[#32363D]">
          <div className="relative flex-auto overflow-auto">
            <div className="input-container flex h-full flex-auto flex-col gap-[16px]">
              <div className="inline-flex items-center justify-start gap-1 self-stretch py-2.5">
                <div className="flex flex-1 items-center justify-start gap-0.5">
                  <div className="justify-start font-['Inter'] text-xs leading-none font-semibold text-black capitalize">
                    {originalImage ? "Original" : "Select an Image to Upload"}
                  </div>
                </div>
              </div>
              <DragNDrop
                originalImage={originalImage}
                onFileChange={onFileChange}
              />
            </div>
          </div>
          <div className="dark:bg-black-100 flex h-[100px] w-full max-w-full flex-col items-center justify-between gap-[8px] bg-white pt-[8px] md:h-[64px] md:flex-row">
            <Button
              className="w-full md:w-auto"
              variant="outline"
              size="lg"
              disabled={isGenerating}
              onClick={resetAll}
            >
              <GrPowerReset size={17} className="mr-[10px]" />
              Reset
            </Button>
            <Button
              variant="primary"
              className="w-full md:w-auto"
              size="lg"
              disabled={isGenerating || file.length === 0}
              onClick={handleRemoveBackground}
            >
              <FaWandMagicSparkles size={15} className="mr-[10px]" />
              Remove Background
            </Button>
          </div>
        </div>

        <div className="relative h-full w-full max-w-full overflow-hidden rounded-[17px] border border-[#E4E4E7] px-[14px] py-[16px] md:w-[492px] dark:border-[#32363D]">
          {image || isGenerating ? (
            <Preview
              isGenerating={isGenerating}
              originalImage={originalImage}
              image={image}
              toggleImage={() => toggleImage(true)}
              onDownload={() => onDownload()}
              onDelete={() => resetAll()}
            />
          ) : (
            <ImagePlaceholder />
          )}
        </div>
      </div>
      <ViewImage
        src={image}
        isOpen={viewImageisOpen}
        onClose={() => toggleImage(false)}
      />
      <ToastContainer
        position="top-center"
        autoClose={false}
        hideProgressBar={true}
        closeOnClick={true}
        pauseOnHover={false}
        draggable={false}
        theme="colored"
        transition={Bounce}
      />

      <PricingPopup
        isVisible={showPricing}
        setIsVisible={setShowPricing}
        appName="Remove Background"
      ></PricingPopup>

      <LimitedUsageModal
        show={showLimitedUsageModal}
        setShowPricing={setShowPricing}
        desc={desc}
      />
    </div>
  );
}
export default App;
