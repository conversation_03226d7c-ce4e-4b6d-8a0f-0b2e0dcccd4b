import asyncio
import os
import httpx
from dotenv import load_dotenv
load_dotenv()
TOTAL_APPS = None
lock = asyncio.Lock()

async def fetch_total_apps():

    app_host_api = os.getenv("APP_HOST_API", "http://localhost:8001")

    url = app_host_api + "/e/get-total-app/"

    async with httpx.AsyncClient() as client:
        response = await client.post(url)
        data = response.json()
        return data["total"]

async def startup_init():
    global TOTAL_APPS
    try:
        TOTAL_APPS = await fetch_total_apps()
        print("Initialized at startup")
    except Exception as e:
        print(f"Startup init failed: {e}")

async def get_total_apps():
    global TOTAL_APPS
    if TOTAL_APPS is None:
        async with lock:
            if TOTAL_APPS is None:
                try:
                    TOTAL_APPS = await fetch_total_apps()
                    print("Lazy initialized")
                except Exception as e:
                    print(f"Lazy init failed: {e}")
                    raise
    return TOTAL_APPS
