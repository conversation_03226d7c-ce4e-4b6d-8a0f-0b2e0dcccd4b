import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "51",
  height = "50",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      width={size || width}
      height={size || height}
      viewBox="0 0 51 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_6101_494)">
        <path
          d="M50.5 25C50.5 11.1929 39.3071 0 25.5 0C11.6929 0 0.5 11.1929 0.5 25C0.5 38.8071 11.6929 50 25.5 50C39.3071 50 50.5 38.8071 50.5 25Z"
          fill="#3073D5"
          fillOpacity="0.1"
        />
        <mask
          id="mask0_6101_494"
          style={{ maskType: "luminance" }}
          maskUnits="userSpaceOnUse"
          x="8"
          y="8"
          width="35"
          height="34"
        >
          <path
            d="M42.1666 8.33325H8.83325V41.6666H42.1666V8.33325Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_6101_494)">
          <path
            d="M37.9998 14.9643V38.3333C37.9998 40.1742 36.5075 41.6666 34.6665 41.6666H16.3333C14.4924 41.6666 13 40.1742 13 38.3333V11.6666C13 9.82567 14.4924 8.33325 16.3333 8.33325H30.7806L37.9998 14.9643Z"
            fill="#3073D5"
          />
          <g filter="url(#filter0_d_6101_494)">
            <path
              d="M38.0001 14.9643H31.6141C31.1538 14.9643 30.7808 14.5912 30.7808 14.1309V8.33325L38.0001 14.9643Z"
              fill="#D2E4FB"
            />
          </g>
          <path
            d="M19.8428 28.0683V33.3333H18.5603V28.0683H19.8428ZM26.7347 28.0683V33.3333H25.4522V30.1758L24.2747 33.3333H23.2397L22.0547 30.1683V33.3333H20.7722V28.0683H22.2872L23.7647 31.7133L25.2272 28.0683H26.7347ZM31.152 29.7333C31.057 29.5583 30.9195 29.4258 30.7395 29.3358C30.5645 29.2408 30.357 29.1933 30.117 29.1933C29.702 29.1933 29.3695 29.3308 29.1195 29.6058C28.8695 29.8758 28.7445 30.2383 28.7445 30.6933C28.7445 31.1783 28.8745 31.5583 29.1345 31.8333C29.3995 32.1033 29.762 32.2383 30.222 32.2383C30.537 32.2383 30.802 32.1583 31.017 31.9983C31.237 31.8383 31.397 31.6083 31.497 31.3083H29.8695V30.3633H32.6595V31.5558C32.5645 31.8758 32.402 32.1733 32.172 32.4483C31.947 32.7233 31.6595 32.9458 31.3095 33.1158C30.9595 33.2858 30.5645 33.3708 30.1245 33.3708C29.6045 33.3708 29.1395 33.2583 28.7295 33.0333C28.3245 32.8033 28.007 32.4858 27.777 32.0808C27.552 31.6758 27.4395 31.2133 27.4395 30.6933C27.4395 30.1733 27.552 29.7108 27.777 29.3058C28.007 28.8958 28.3245 28.5783 28.7295 28.3533C29.1345 28.1233 29.597 28.0083 30.117 28.0083C30.747 28.0083 31.277 28.1608 31.707 28.4658C32.142 28.7708 32.4295 29.1933 32.5695 29.7333H31.152Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_6101_494"
          x="27.7808"
          y="7.33325"
          width="11.2192"
          height="10.6311"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="-1" dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_6101_494"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_6101_494"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_6101_494">
          <rect
            width="50"
            height="50"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
