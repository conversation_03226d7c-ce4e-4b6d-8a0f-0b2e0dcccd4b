import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "24",
  height = "24",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      width={size || width}
      height={size || height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g
        clip-path="url(#paint0_angular_1538_35927_clip_path)"
        data-figma-skip-parse="true"
      >
        <g transform="matrix(0.009 0 0 0.009 12 12.3999)">
          <foreignObject
            x="-1111.11"
            y="-1111.11"
            width="2222.22"
            height="2222.22"
          >
            <div
              style={{
                background:
                  "conic-gradient(from 90deg, rgba(0, 0, 0, 0) 0deg, rgba(0, 0, 0, 0) 0.036deg, rgba(0, 0, 0, 1) 360deg)",
                height: "100%",
                width: "100%",
                opacity: 1,
              }}
            ></div>
          </foreignObject>
        </g>
      </g>
      <path
        d="M12 3.3999C16.9706 3.3999 21 7.42934 21 12.3999C21 17.3705 16.9706 21.3999 12 21.3999C7.02944 21.3999 3 17.3705 3 12.3999C3 7.42934 7.02944 3.3999 12 3.3999ZM12 4.8999C7.85786 4.8999 4.5 8.25777 4.5 12.3999C4.5 16.542 7.85786 19.8999 12 19.8999C16.1421 19.8999 19.5 16.542 19.5 12.3999C19.5 8.25777 16.1421 4.8999 12 4.8999Z"
        data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:0.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:0.0},&#34;position&#34;:9.9999997473787516e-05},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:0.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:0.0},&#34;position&#34;:9.9999997473787516e-05},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:18.0,&#34;m01&#34;:1.7089392848426178e-14,&#34;m02&#34;:3.0,&#34;m10&#34;:-9.0957879181579986e-15,&#34;m11&#34;:18.0,&#34;m12&#34;:3.399902343750},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M20.0858 10.6005C20.4966 10.5475 20.8726 10.8375 20.9256 11.2484C20.9748 11.6302 20.9995 12.0149 20.9995 12.4C20.9995 12.8142 20.6638 13.15 20.2495 13.15C19.8353 13.15 19.4995 12.8142 19.4995 12.4C19.4995 12.0791 19.4789 11.7585 19.4379 11.4403C19.3849 11.0295 19.6749 10.6535 20.0858 10.6005Z"
        fill={fill}
      />
      <defs>
        <clipPath id="paint0_angular_1538_35927_clip_path">
          <path d="M12 3.3999C16.9706 3.3999 21 7.42934 21 12.3999C21 17.3705 16.9706 21.3999 12 21.3999C7.02944 21.3999 3 17.3705 3 12.3999C3 7.42934 7.02944 3.3999 12 3.3999ZM12 4.8999C7.85786 4.8999 4.5 8.25777 4.5 12.3999C4.5 16.542 7.85786 19.8999 12 19.8999C16.1421 19.8999 19.5 16.542 19.5 12.3999C19.5 8.25777 16.1421 4.8999 12 4.8999Z" />
        </clipPath>
      </defs>
    </svg>
  );
}
