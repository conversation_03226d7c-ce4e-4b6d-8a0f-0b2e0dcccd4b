import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "14",
  height = "14",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      width={size || width}
      height={size || height}
      viewBox="0 0 42 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1596_34126)">
        <path
          d="M41.5 31.6119C39.2072 33.0994 35.3318 35.1204 30.6277 34.8844C21.338 34.4187 17.1012 25.5586 9.16355 26.4899C7.09813 26.7324 4.01402 27.7283 0.5 31.6119V28.3783C3.65265 25.135 6.53427 24.0291 8.5623 23.6119C16.6651 21.9434 21.6869 29.1188 32.8022 29.4939C36.4938 29.62 39.5374 28.9507 41.5031 28.3783V31.6119H41.5Z"
          fill={fill}
        />
        <path
          d="M38.3847 39.9999H3.61526C1.89564 39.9999 0.5 38.5513 0.5 36.7663C3.15109 31.4761 5.95483 29.8819 7.95794 29.371C14.014 27.8221 18.2383 34.7453 28.0981 37.0056C33.5592 38.257 38.3692 37.5359 41.5031 36.7663C41.5031 38.5513 40.1075 39.9999 38.3878 39.9999H38.3847Z"
          fill={fill}
        />
        <path
          d="M37.4657 0H4.53738C2.30685 0 0.5 1.87551 0.5 4.19078V21.9563C3.95171 20.3945 6.89252 19.9289 9.16355 19.8577C18.2726 19.5635 22.6558 25.3517 33.6776 26.3864C36.5623 26.658 39.2009 26.5352 41.5 26.2377V4.19078C41.5 1.87551 39.6931 0 37.4626 0H37.4657ZM30.0607 16.6726C27.0327 16.6726 24.5748 14.1245 24.5748 10.9782C24.5748 7.83185 27.0296 5.28375 30.0607 5.28375C33.0919 5.28375 35.5467 7.83185 35.5467 10.9782C35.5467 14.1245 33.0919 16.6726 30.0607 16.6726Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_1596_34126">
          <rect
            width="41"
            height="40"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
