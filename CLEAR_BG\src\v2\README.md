# 🚀 Fullstack App: FastAPI + React (Vite)

This project is a fullstack boilerplate using:

- 🧠 **FastAPI** for the backend API
- ⚛️ **React (Vite)** for the frontend UI
- 🧪 **concurrently** to run both servers in development
- ⚙️ `.env` support for both frontend and backend

---

## 📦 Project Structure

```
fullstack-app/
├── backend/            # FastAPI backend (Python)
│   ├── main.py
│   ├── controllers.py
│   ├── requirements.txt 
│   └── .env            # (excluded from Git)
│
├── frontend/           # React frontend (Vite)
│   ├── src/
│   ├── dist/           # Production build output
│   └── .env            # React env variables (e.g. VITE_START_URL)
│
├── package.json        # Root scripts (dev/start/install-all)
└── README.md
```

---

## 🔧 Setup Instructions

### 1. Clone the repo

```bash
cd CLEAR_BG\src\v2
```

---

### 2. Setup environment variables

Create `.env` files in:

#### `backend/.env`

```env
CORS_ORIGINS=["http://localhost:5173", "http://127.0.0.1:9010", "http://localhost:9010", "https://staging.clearbg.ai-pro.org", "https://removebg.ai-pro.org"]
```

#### `frontend/.env`

```env
VITE_START_URL = "https://start.ai-pro.org"
VITE_ENVIRONMENT = false
```

---

### 📥Install dependencies

Install everything (backend + frontend):

```bash
npm run install-all
```

> This will:
>
> - Install React/Vite dependencies (`frontend/`)
> - Install Python backend dependencies using

---

## 🧪 Run in Development

Run both frontend + backend in parallel (with hot reload):

```bash
npm run dev
```

- React: [http://localhost:5173](http://localhost:5173)
- API: [http://localhost:9010/api](http://localhost:9010/api)

---

## 🚀 Build for Production

Build the frontend app into `frontend/dist/`:

```bash
npm run build
```

Then run the backend server, which serves both API and static frontend:

```bash
npm start
```

- Production server runs at: [http://localhost:9010](http://localhost:9010)

---

## 🧰 Scripts Overview

| Command                 | Description                         |
| ----------------------- | ----------------------------------- |
| `npm run install-all` | Install frontend + backend deps     |
| `npm run dev`         | Start both servers for development  |
| `npm run build`       | Build React frontend for production |
| `npm start`           | Serve production frontend + API     |

---

## 🛠 Tech Stack

- **Frontend**: React + Vite
- **Backend**: FastAPI + Uvicorn
- **Dev Tools**: concurrently
- **Env Management**: dotenv (`.env` files)

---

## 💡 Notes

- CORS is enabled in dev so React can call the API

---

## 🧑‍💻 Author

Built by Full Stack Developer -> Jerald Lim
