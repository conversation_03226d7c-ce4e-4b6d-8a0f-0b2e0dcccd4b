# app/utils/cookie_utils.py

from fastapi import Request, Response
from contextvars import ContextVar

# Contexts for request and response
_request_context: ContextVar[Request] = ContextVar("request_context")
_response_context: ContextVar[Response] = ContextVar("response_context")

# Middleware for FastAPI (add this in main.py)
async def add_request_response_to_context(request: Request, call_next):
    # Create an empty Response so we can access it inside the handler
    response = Response("Internal server error", status_code=500)
    token_request = _request_context.set(request)
    token_response = _response_context.set(response)
    try:
        response = await call_next(request)
        _response_context.set(response)  # update with final response
        return response
    finally:
        _request_context.reset(token_request)
        _response_context.reset(token_response)

# Reusable: Get cookie
def get_cookie(key: str, default=None):
    request = _request_context.get()
    return request.cookies.get(key, default)

# Reusable: Set cookie
def set_cookie(key: str, value: str, max_age: int = 2592000, secure: bool = True, httponly: bool = True, path: str = "/", domain: str = None):
    response = _response_context.get()
    response.set_cookie(
        key=key,
        value=value,
        max_age=max_age,
        secure=secure,
        httponly=httponly,
        path=path,
        domain=domain
    )

# Reusable: Delete cookie
def delete_cookie(key: str, path: str = "/", domain: str = None):
    response = _response_context.get()
    response.delete_cookie(
        key=key,
        path=path,
        domain=domain
    )
