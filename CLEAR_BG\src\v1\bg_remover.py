import os
import io
import tempfile
import time
import uuid
import zipfile
from pathlib import Path
import base64
import streamlit as st
from PIL import Image
from rembg import remove
import toml
import requests
import extra_streamlit_components as stx
from streamlit_javascript import st_javascript
import re

session = requests.session()
cookies_is_set = False
cookies = None
IS_FREE_USER = 1

MAX_FILES = 5
MULTIPLE_IMAGES_ALLOWED = True
ALLOWED_TYPES = ["png", "jpg", "jpeg", "jfif", "gif"]
logo_path = "assets/images/logo.png"
icon_path = "assets/images/favi.png"
favicon_path = Image.open("assets/images/favicon_clearbg.ico")


st.set_page_config(
    page_title="Remove Background | AI-Pro.org",
    page_icon=favicon_path,
    initial_sidebar_state="expanded",
)

if not os.path.exists("secrets.toml"):
    APP_HOST_API = st.secrets.api_keys["APP_HOST_API"]
    UPGRADE_URL = st.secrets.api_keys["UPGRADE_URL"]
    WP_BASE_URL = st.secrets.api_keys["WP_BASE_URL"]
else:
    secrets = toml.load("secrets.toml")["api_keys"]
    APP_HOST_API = secrets["APP_HOST_API"]
    UPGRADE_URL = secrets["UPGRADE_URL"]
    WP_BASE_URL = st.secrets.api_keys["WP_BASE_URL"]

apiGetUsageUrl = APP_HOST_API + "/e/get-usage/"
apiSetUsageUrl = APP_HOST_API + "/e/set-usage/"
apiGetTotalAppUrl = APP_HOST_API + "/e/get-total-app/"


def remove_bg(input_data, path):
    """Remove background from an image using rembg."""
    result = remove(input_data)
    img = Image.open(io.BytesIO(result)).convert("RGBA")
    if Path(path).suffix != ".png":
        img.LOAD_TRUNCATED_IMAGES = True
    return img


def build_ui():
    """Show UI of the app with file upload form"""
    logo_bytes = Path(logo_path).read_bytes()
    logo_base64 = base64.b64encode(logo_bytes).decode("utf-8")
    icon_bytes = Path(icon_path).read_bytes()
    icon_base64 = base64.b64encode(icon_bytes).decode("utf-8")
    logo_html = f'<img src="data:image/png;base64,{logo_base64}" alt="AI-Pro Logo" style="width: 200px;" class="aipro_logo">'
    title_html = f"<h2>Image Background Remover</h2>"
    st.sidebar.markdown(logo_html, unsafe_allow_html=True)
    st.markdown(title_html, unsafe_allow_html=True)
    st.markdown("Remove background from images using pre-trained ML model.")

    st.markdown("\n")
    st.sidebar.markdown("---")

    uploaded_files = st.sidebar.file_uploader(
        f"Choose one or multiple images (max: {MAX_FILES})",
        type=ALLOWED_TYPES,
        accept_multiple_files=MULTIPLE_IMAGES_ALLOWED,
        key=st.session_state["key"],
    )

    return uploaded_files


def get_manager():
    return stx.CookieManager()


def get_cookie():
    cookie_manager = get_manager()
    cookies = cookie_manager.get_all()
    return cookies


def get_aiApp():
    absoluteUrl = st_javascript(
        "await fetch('').then(r => window.parent.location.hostname)"
    )
    if absoluteUrl != 0 and "app" in absoluteUrl:
        relativeURL = absoluteUrl + "/"
        return relativeURL
    elif absoluteUrl != 0:
        return absoluteUrl


aiApp = get_aiApp()


def get_usage(apiUrl="http://localhost:8001/e/get-usage"):
    global cookies
    limit = cookies.get("WcvYPABR")
    if limit:
        limit = re.sub(r"\D", "", limit)
        limit = int(limit)
    else:
        limit = 3

    aiUser = cookies.get("aiwp_logged_in")
    ipdflu = cookies.get('ipdflu')
    session.headers.update({"Content-Type": "application/json"})
    session.cookies.set("WcvYPABR", limit)
    limitedUsage = session.cookies.get("WcvYPABR")
    session.cookies.set("aiwp_logged_in", aiUser)
    session.cookies.set("aiwp_app_id", aiApp)
    objBody = {"aiwp_logged_in": aiUser, "aiwp_app_id": aiApp, "ipdflu": ipdflu}

    headers = {
        "content-type": "application/json",
    }
    try:
        response = requests.post(apiUrl, json=objBody, headers=headers)

        data = response.json()

        ## for debuging
        # with open('response.txt', 'w') as json_file:
        #     if response:
        #         json.dump(response.json(), json_file, indent=4)
        #     else:
        #         file = open("response.txt", "a")
        #         file.write(str(response))
        #         file.close()
        ##for debuging

        if data["usage"] is not None:
            TRIED_USAGE = int(data["usage"])
        else:
            TRIED_USAGE = 0

        session.headers.update({"Content-Type": "application/json"})
        session.cookies.set("tried_remote", TRIED_USAGE)

        return TRIED_USAGE

    except Exception as error:
        return 0


def set_usage(apiUrl="http://localhost:8001/e/set-usage"):
    global cookies
    aiUser = cookies.get("aiwp_logged_in")
    ipdflu = cookies.get("ipdflu")
    limitedUsage = session.cookies.get("WcvYPABR")
    objBody = {
        "aiwp_logged_in": aiUser,
        "aiwp_app_id": aiApp,
        "usage_tries": limitedUsage,
        "ipdflu": ipdflu,
    }
    headers = {
        "content-type": "application/json",
    }
    try:
        response = requests.post(apiUrl, json=objBody, headers=headers)
        data = response.json()
        return data
    except Exception as error:
        return error


def isFreeUser():
    global cookies
    global cookies_is_set
    global IS_FREE_USER
    if cookies_is_set == False:
        cookies = get_cookie()
        cookies_is_set = True

    if cookies.get("user_email"):
        IS_FREE_USER = 0
    
    if cookies.get("amjhcxhin"):
        IS_FREE_USER = bool(cookies.get("amjhcxhin"))


def get_image_bytes(uploaded_files):
    """Return bytes data for each uploaded file."""
    img_bytes = []
    for uploaded_file in uploaded_files:
        bytes_data = uploaded_file.getvalue()
        if "btn" not in st.session_state:
            img_bytes.append((uploaded_file, bytes_data))
    return img_bytes


def getTotalApp():
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(apiGetTotalAppUrl, headers=headers)
        data = response.json()
        return data["total"]
    except Exception as error:
        return error


def is_limited(apiGetUsageUrl, apiSetUsageUrl):
    global cookies
    global cookies_is_set
    if cookies_is_set == False:
        cookies = get_cookie()
        cookies_is_set = True

    totalApps = getTotalApp()
    TRIED_USAGE = get_usage(apiGetUsageUrl)
    if IS_FREE_USER == 1:
        limitedUsage = session.cookies.get("WcvYPABR")
        wpCTAFlag = cookies.get("regRedirectWP")
        wp_flow = cookies.get("flow")
        if wpCTAFlag == 1:
            if wp_flow == '02':
                register_logic_url = '/register-b'
            elif wp_flow == '03':
                register_logic_url = '/register-x'
            elif wp_flow == '04':
                register_logic_url = '/select-account-type-d'
            elif wp_flow == '05':
                register_logic_url = '/aurora-register'
            else:
                register_logic_url = '/register'
        else:
            register_logic_url = '/register'
            
        ctaModal_logic = WP_BASE_URL+register_logic_url if wpCTAFlag == 1 else UPGRADE_URL
        if TRIED_USAGE >= limitedUsage:
            st.write(
                f"""
                <div id="modal-container" class="display-block">
                    <div id="modal-content">
                        <div id="modal-subtitle">Enjoying the trial version of Remove Background so far?</div>
                        <div id="modal-title">Switch to PRO  to continue using Remove Background</div>
                        <p id="modal-desc">Switch now to PRO and get access to <span>{totalApps}</span> different creativity and productivity AI tools.</p>
                        <a id="modal-cta" href="{ctaModal_logic}" target="_parent">CONTINUE</a>
                    </div>
                </div>
                """,
                unsafe_allow_html=True,
            )
            return True
        else:
            set_usage(apiSetUsageUrl)

    return False


def main():
    uploaded_files = build_ui()

    if len(uploaded_files) > MAX_FILES:
        st.warning(
            f"Maximum number of images reached! Only the first {MAX_FILES} will be processed."
        )
        uploaded_files = uploaded_files[:MAX_FILES]

    if not uploaded_files:
        return

    progress_bar = st.empty()

    image_row = st.empty()  # row to hold original and result image
    original, result = image_row.columns(2)
    st.text("\n")

    # show original images
    img_bytes = get_image_bytes(uploaded_files)
    for b in img_bytes:
        original.image(b[1], caption="Original")

    nobg_images = []  # result images with no background

    remove_bg_clicked = st.sidebar.button("Remove Background", type="primary")
    new_image_clicked = st.sidebar.button("New Image")

    if not remove_bg_clicked and not new_image_clicked:
        return

    if remove_bg_clicked:
        showModal = is_limited(apiGetUsageUrl, apiSetUsageUrl)
        if showModal:
            return

        progress_text = "Operation in progress. Please wait."
        progress = progress_bar.progress(0, text=progress_text)

        with st.spinner("Processing image..."):
            for i, image_byte in enumerate(img_bytes, start=1):
                uploaded_file, bytes_data = image_byte
                if isinstance(uploaded_file, int):
                    img_path = Path(str(uploaded_file) + ".png")
                else:
                    img_path = Path(uploaded_file.name)

                img = remove_bg(bytes_data, img_path)
                with io.BytesIO() as f:
                    img.save(f, format="PNG", quality=100, subsampling=0)
                    data = f.getvalue()

                nobg_images.append((img, img_path, data))
                cur_progress = int(100 / len(img_bytes))
                progress.progress(cur_progress * i)

            time.sleep(1)
            progress_bar.empty()
            progress.success("Complete!")

    elif new_image_clicked:
        st.session_state["key"] = session_id
        st.experimental_rerun()

    if len(nobg_images) > 0:
        for nobg_image in nobg_images:
            result.image(nobg_image[0], caption="Result")
    if len(nobg_images) > 1:
        with io.BytesIO() as tmp_zip:
            with zipfile.ZipFile(tmp_zip, "w") as z:
                for img, path, data in nobg_images:
                    with tempfile.NamedTemporaryFile() as fp:
                        img.save(fp.name, format="PNG")
                        z.write(
                            fp.name,
                            arcname=path.name,
                            compress_type=zipfile.ZIP_DEFLATED,
                        )
            zip_data = tmp_zip.getvalue()
        st.markdown(
            """
            <style>
                .stDownloadButton > button, .dlbtn {
                    background: #16A34A !important;
                    width: 240px !important;
                    padding: 8px !important;
                    color: #ffffff;
                }
                .stDownloadButton > button:hover {
                    color: #dddddd;
                }
            </style>
            """,
            unsafe_allow_html=True,
        )
        st.download_button(
            label=f"Download as ZIP",
            data=zip_data,
            file_name=f"results_{int(time.time())}.zip",
            mime="application/zip",
            key=str(uuid.uuid4()),
            on_click=display_result,
            args=(nobg_images, img_bytes, image_row),
        )
    else:
        try:
            output = nobg_images[0]
            st.markdown(
                """
                <style>
                    .stDownloadButton > button {
                        background: #16A34A !important;
                        color: #ffffff;
                        width: 240px !important;
                        padding: 8px !important;
                    }
                    .stDownloadButton > button:hover {
                        color: #dddddd;
                    }
                </style>
                """,
                unsafe_allow_html=True,
            )
            st.download_button(
                label="Download Result",
                data=output[-1],
                file_name=f"{output[1].stem}_nobg.png",
                mime="image/png",
                key=str(uuid.uuid4()),
                on_click=display_result,
                args=(nobg_images, img_bytes, image_row),
            )
        except IndexError:
            st.error("No more images to process!")

    time.sleep(3)
    progress_bar.empty()


def display_result(nobg_images, uploaded_images, container):
    col1, col2 = container.columns(2)

    with col1:
        for image_data in uploaded_images:
            st.image(image_data[1], caption="Original")

    with col2:
        for i, image_data in enumerate(nobg_images):
            st.image(image_data[0], caption="Result")

    time.sleep(3)
    # Add a new row in col1
    col1.write(
        "<div style='display: flex; align-items: center; margin-top: 10px;'>",
        unsafe_allow_html=True,
    )

    # Create a download button for the result image in col1
    if len(nobg_images) > 1:
        with io.BytesIO() as tmp_zip:
            with zipfile.ZipFile(tmp_zip, "w") as z:
                for img, path, data in nobg_images:
                    with tempfile.NamedTemporaryFile() as fp:
                        img.save(fp.name, format="PNG")
                        z.write(
                            fp.name,
                            arcname=path.name,
                            compress_type=zipfile.ZIP_DEFLATED,
                        )
            zip_data = tmp_zip.getvalue()
        download_button = f"<a href='data:application/zip;base64,{base64.b64encode(zip_data).decode('utf-8')}' download='results.zip' class='dlbtn'>Download as ZIP</a>"
    else:
        download_button = f"<a href='data:image/png;base64,{base64.b64encode(image_data[-1]).decode('utf-8')}' download='result_image_{i+1}.png' class='dlbtn'>Download Result</a>"
    col1.write(download_button, unsafe_allow_html=True)

    # Close the row in col1
    col1.write("</div>", unsafe_allow_html=True)


if __name__ == "__main__":
    st.markdown(
        """
        <style>
            .main > .block-container:nth-child(1) .element-container div button, .css-yuufvt {
                right: 0.5rem !important;
                top: 0.5rem !important;
            }
            .streamlit-paginator small, .streamlit-paginator > div {
                color: #bbb;
            }
            .streamlit-paginator > div > button:hover {
                color: #fff;
            }
            .dlbtn, .dlbtn:hover {
                background: #16A34A !important;
                width: 240px !important;
                padding: 11px 65px !important;
                color: #fff !important;
                margin-top: 25px;
                text-decoration: none;
                border-radius: 5px;
                border: 1px solid green;
            }
            .dlbtn:hover {
                opacity: 0.9;
            }
            h2 {
                font-weight: bolder;
            }
            hr {
                background: #ffffff;
            }
            .element-container:nth-child(3) > div > label > div > p {
                color: #ffffff !important;
            }
            .element-container:nth-child(3) > div > section {
                background: #262730;
                color: #ffffff;
            }
            .element-container:nth-child(3) > div > section > div > div > small {
                background: #262730;
                color: #cccccc;
            }
            .element-container:nth-child(3) > div > section > button {
                background: #1b1c20;
                color: #cccccc;
            }
            .element-container:nth-child(3) > div > section:focus {
                box-shadow: none !important;
            }
            .uploadedFile > div{
                color: #cccccc;
            }
            .uploadedFileData > small {
                color: #bbbbbb;
                font-size: 12px !important;
            }
            .css-1544g2n{
                padding-top:40px;
            }
            .aipro_logo {
                margin: 0 auto;
                text-align:center;
                display:block;
            }
            footer {
                visibility: hidden;
            }
            #MainMenu {
                visibility: hidden;
            }
            .block-container:first-child {
                margin-top: -10px;
            }
            .block-container > div > div > div {
                margin-bottom: 10px;
            }
            # .main > .block-container:nth-child(1) > div > div > div:nth-child(7) > div > div > div > div > div > div,
            # .main > .block-container:nth-child(1) > div > div > div:nth-child(6) > div > div > div > div > div > div{
            #     background: red !important;
            #     color: red !important;
            # }
            .main > .block-container:nth-child(1) > div > div > div:nth-child(7) > div > div > div > div > div > div,
            .main > .block-container:nth-child(1) > div > div > div:nth-child(6) > div > div > div > div > div > div,
            .main > .block-container:nth-child(0) > div > div > div:nth-child(7) > div > div > div > div > div > div,
            .main > .block-container:nth-child(0) > div > div > div:nth-child(6) > div > div > div > div > div > div,
            .main > .block-container:nth-child(0) > div > div > div:nth-child(7) > div > div > div > div > div > div > div,
            .main > .block-container:nth-child(0) > div > div > div:nth-child(6) > div > div > div > div > div > div > div{
                display: block !important;
                width: auto !important;
            }
            .appview-container > section:first-child > div:first-child {
                /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#002100+0,000f00+19,000500+69 */
                background: rgb(0,33,0); /* Old browsers */
                background: -moz-linear-gradient(45deg,  rgba(0,33,0,1) 0%, rgba(0,15,0,1) 19%, rgba(0,5,0,1) 69%); /* FF3.6-15 */
                background: -webkit-linear-gradient(45deg,  rgba(0,33,0,1) 0%,rgba(0,15,0,1) 19%,rgba(0,5,0,1) 69%); /* Chrome10-25,Safari5.1-6 */
                background: linear-gradient(45deg,  rgba(0,33,0,1) 0%,rgba(0,15,0,1) 19%,rgba(0,5,0,1) 69%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
                filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#002100', endColorstr='#000500',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
            }
            .appview-container > section:first-child > div:first-child > div:first-child > button:first-child  {
                color: #808080 !important;
            }

            .row-widget.stButton > button:first-child {
                background: #16A34A;
                color: #fff;
                border: none;
                width:100%;
                padding: 10px;
            }
            .element-container > div > section > button:hover, .element-container > div > section > button:active,
            button:focus:not(:active) {
                color: #dddddd !important;
                border: 1px solid #ffffff !important;
                box-shadow: none !important;
            }

            button:active {
                box-shadow: none !important;
            }
            .element-container:nth-child(5) > .row-widget.stButton > button:first-child {
                background: #16A34A;
            }
            .row-widget.stButton > button:first-child:hover {
                opacity: 0.9;
                border: none;
            }
            .element-container > div > section > button {
                width: 100% !important;
            }
    
            #root > div:nth-child(1) > div.withScreencast > div:nth-child(1) > div:nth-child(1) > div > section.main > div.block-container > div:nth-child(1) > div:nth-child(1) > div:nth-child(9) > div:nth-child(1) > div:nth-child(1) > div > div > div > div  {
                display: block !important;
            }

              #root > div:nth-child(1) > div.withScreencast > div:nth-child(1) > div:nth-child(1) > div > section.main > div.block-container > div:nth-child(1) > div:nth-child(1) > div:nth-child(9) > div:nth-child(2) > div:nth-child(1) > div > div > div > div  {
                display: block !important;
            }

            #root > div:nth-child(1) > div.withScreencast > div:nth-child(1) > div:nth-child(1) > div > section.main > div.block-container > div:nth-child(1) > div:nth-child(1) > div:nth-child(9) > div > div:nth-child(1) > div > div > div > button {
                box-shadow: -1px 2px 4px   rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
            }
            #root > div:nth-child(1) > div.withScreencast > div:nth-child(1) > div:nth-child(1) > div > section.main > div.block-container > div:nth-child(1) > div:nth-child(1) > div:nth-child(13) > div >  button {
                 border:none !important;
            }
            
            a,
            div a:hover {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
            }

            .display-block {
                display: block !important;
            }

            #modal-container a {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
            }

        </style>
        """,
        unsafe_allow_html=True,
    )
    session_id = str(uuid.uuid4())
    if "key" not in st.session_state:
        st.session_state["key"] = session_id

    isFreeUser()
    main()
