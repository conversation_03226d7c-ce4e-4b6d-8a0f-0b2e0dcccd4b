export default function LimitedUsageModal({
  desc,
  show,
  setShowPricing,
}: {
  desc: string;
  show: boolean;
  setShowPricing: (value: boolean) => void;
}) {
  return (
    <div id="modal-container" className={show ? "show" : "hidden"}>
      <div id="modal-content">
        <div id="modal-subtitle">
          Enjoying the trial version of Remove Background so far?
        </div>
        <h2 id="modal-title">Switch to PRO to Continue Using Remove Background</h2>
        <p id="modal-desc" dangerouslySetInnerHTML={{ __html: desc }}></p>
        <a
          id="modal-cta"
          href="#"
          onClick={() => setShowPricing(true)}
          target="_parent"
        >
          CONTINUE
        </a>
      </div>
    </div>
  );
}
