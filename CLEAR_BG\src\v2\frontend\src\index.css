@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@import "tailwindcss";

@theme {

  --font-inter: "Inter", sans-serif;

  --color-black: #000;
  --color-black-100: #1A1D21;
  --color-black-150: #22252A;
  --color-black-200: #32363D;
  --color-black-300: #4A4A4A;
  --color-gray: #E4E4E7;

  --color-white-200: #EEEFEF;
  --color-white-100: #F1F1F1;
  --color-white-50: #F5F5F5;
  --color-white: #fff;


  /* applied in button */
  --color-primary: #3073D5;
  --color-primary-hover: #1E63C6;

  --color-secondary: #FFF;
  --color-secondary-hover: #FCFCFC;

  --radius-2lg: 10px;
}

@layer base {
  :root {
    font-family: "Inter", sans-serif;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 
    --color-black: #000;
    --color-black-100: #1a1d21;
    --color-black-200: #333538;
    --color-black-300: #4A4A4A;
    --color-black-400: #646464;
    --color-black-500: #797979;
    --color-black-600: #9A9A9A;
    --color-black-700: #BDBDBD;
    --color-black-800: #D9D9D9;
    --color-black-900: #F1F1F1;
    --color-black-950: #ffffff;
    --color-white: #fff;
    --color-gray: #E4E4E7;
    --color-white-100: #F1F1F1;
    --color-white-200: #D9D9D9;
    --color-white-300: #BDBDBD;
    --color-white-400: #9A9A9A;
    --color-white-500: #797979;
    --color-white-600: #646464;
    --color-white-700: #4A4A4A;
    --color-white-800: #333538;
    --color-white-900: #1a1d21;
    --color-white-950: #000000; 
    */

  }

  body {
    @apply bg-white text-black dark:bg-black-100 dark:text-white;
    @apply min-h-screen;
  }
}

.bg-gradient-blue {
  background: linear-gradient(90deg, #3e53b8 0%, #2393f1 100%);
}

.bg-gradient-black {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.88) 100%);
}

@layer components {
  /* Custom component styles can be added here */

  .heading-1 {
    font-size: 32px;
    font-weight: 600;
  }

  .heading-2 {
    font-size: 24px;
  }

  .label-1 {
    @apply text-black-100 dark:text-white-100;
    font-size: 14px;
    font-weight: 600;
  }

  .label-2 {
    font-size: 12px;
    color: #D8D8D8;
  }

  .sub-title {
    font-size: 16px;
  }

  .caption {
    font-size: 15px;
  }



}

.scroll-1 {
  scrollbar-color: #EEEFEF transparent;
  scrollbar-width: thin;
}


@media (prefers-color-scheme: dark) {
  .scroll-1 {
    scrollbar-color: #32363D transparent;
  }
}


.multiline-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}


#modal-container {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

#modal-container.show {
  display: block;
}

#modal-content {
  position: absolute;
  top: 44%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90%;
  background-color: #0038ff;
  border-radius: 7px;
  width: 500px;
  padding: 30px;
  text-align: center;
  color: #fff;
  box-shadow: 0 5px 18px -7px black;
  font-family: 'Alegreya Sans', sans-serif !important;
  background-image: url('./assets/images/bg.png');
}

div#modal-subtitle {
  margin: 5px 0 10px 0;
}

#modal-title {
  font-size: 24px;
  margin: 15px 0 20px;
  padding: 0 20px;
}

#modal-desc {
  font-size: 16px;
  margin-bottom: 30px;
}

#modal-desc span {
  font-size: 20px;
  font-weight: bold;
}

#modal-cta {
  display: block;
  margin: 0 auto;
  font-size: 18px;
  background-color: #fff;
  color: #052f5e;
  border: none;
  border-radius: 30px;
  padding: 10px;
  cursor: pointer;
  text-decoration: none;
  width: 50%;
  box-shadow: 0 3px 10px -4px black;
  font-weight: bold;
}
