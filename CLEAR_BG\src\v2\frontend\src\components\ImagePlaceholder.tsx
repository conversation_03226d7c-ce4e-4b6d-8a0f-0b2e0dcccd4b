const ImagePlaceholder = () => {
  const baseUrl = window.location.origin;
  const imageUrl = `${baseUrl}/images/placeholder.svg`;
  return (
    <div className="flex h-full flex-col items-center justify-center gap-4">
      <img src={imageUrl} alt="image" />
      <div className="justify-start self-stretch text-center">
        <span className="text-black-100 font-['Inter'] text-base leading-snug font-bold dark:text-white">
          No images generated yet.
          <br />
        </span>
        <span className="font-['Inter'] text-base leading-snug font-normal text-[#3C3A4E] dark:text-[#9A9A9A]">
          Use the form on the left to create images.
        </span>
      </div>
    </div>
  );
};

export default ImagePlaceholder;
