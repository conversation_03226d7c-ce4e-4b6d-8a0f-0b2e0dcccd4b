import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/

export default ({ mode }: { mode: string }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd(), "") };

  const VITE_ENVIRONMENT: string = process.env.VITE_ENVIRONMENT || "true";
  const drop_console: boolean = VITE_ENVIRONMENT === "true";

  return defineConfig({
    plugins: [react(), tailwindcss()],
    build: {
      outDir: "./dist",
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: drop_console,
        },
      },
    },
    server: {
      host: "127.0.0.1",
      allowedHosts: ["local-removebg.ai-pro.org"],
    },
  });
};
