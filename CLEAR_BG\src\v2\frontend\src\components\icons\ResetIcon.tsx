import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "14",
  height = "14",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      width={size || width}
      height={size || height}
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.1667 4.16667C12.1732 2.07022 10.0054 0.625 7.5 0.625C3.97918 0.625 1.125 3.47918 1.125 7C1.125 10.5208 3.97918 13.375 7.5 13.375C11.0208 13.375 13.875 10.5208 13.875 7M13.875 0.625V4.875H9.625"
        stroke={fill}
      />
    </svg>
  );
}
