"""FastAPI backend server for serving API and React frontend."""
from pathlib import Path
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from controllers import router as api_router
import os
import json
from dotenv import load_dotenv
from utils.initializer import startup_init
from contextlib import asynccontextmanager
from utils.cookie_utils import add_request_response_to_context

# Load the .env file
load_dotenv()
# Access environment variables

cors_origins = json.loads(os.getenv("CORS_ORIGINS", "[]"))

@asynccontextmanager
async def lifespan(app: FastAPI):
    await startup_init()
    yield

app = FastAPI(lifespan=lifespan)

# Allow frontend dev server during development (optional)
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
app.middleware("http")(add_request_response_to_context)

# Include API routes
app.include_router(api_router, prefix="/api")

# Path to React build output
build_path = Path(__file__).parent.parent / "frontend" / "dist"

# Serve React static files
app.mount("/", StaticFiles(directory=build_path, html=True), name="static")

# Fallback to index.html for React Router
@app.get("/{full_path:path}")
async def serve_spa(_full_path: str):
    """
    Fallback to index.html for React Router.

    Returns the index.html file from the React build output directory.
    """
    return FileResponse(build_path / "index.html")
