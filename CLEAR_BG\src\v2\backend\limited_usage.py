import re
from utils.cookie_utils import  get_cookie, set_cookie, delete_cookie


def get_limit():
    """
    Gets the limit of the user, from the cookie WcvYPABR.
    If the cookie is not set, it defaults to 3.
    """

    limit = get_cookie("WcvYPABR")
    print("WcvYPABR", limit)
    if limit:
        limit = re.sub(r"\D", "", limit)
        limit = int(limit)
    else:
        limit = 3
    return limit


def get_usage():
    limit = get_limit()
    print("limit", limit)
    set_cookie("testpy", limit)
    aiUser = get_cookie("aiwp_logged_in")
    ipdflu = get_cookie("ipdflu")
    limitedUsage = get_cookie("WcvYPABR")

    return 3
