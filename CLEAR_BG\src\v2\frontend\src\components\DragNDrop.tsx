import React, { useCallback, useMemo } from "react";
import { useDropzone } from "react-dropzone";
import type { FileRejection } from "react-dropzone";

import ImgIcon from "./icons/ImgIcon";
import { cn } from "../utils/cn";
import { toast } from "react-toastify";

const rejectStyle = {
  borderColor: "#ff1744",
};

// drag and drop type
type DragNDropProps = {
  onFileChange: (file: File[]) => void;
  originalImage: string;
};

export default function DragNDrop({
  onFileChange,
  originalImage,
}: DragNDropProps) {
  const fileLimit = 1;
  const maxFileSize = 5; // 5MB
  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: FileRejection[]) => {
      console.log("fileRejections", fileRejections);
      const err: string[] = [];
      const TooManyFiles = "You can only upload 1 file";

      if (fileRejections.length > 0) {
        fileRejections.forEach((fileRejection) => {
          if (fileRejection.errors[0].code.includes("file-invalid-type")) {
            if (!err.includes("File type not supported.")) {
              err.push("File type not supported.");
            }
          } else if (fileRejection.errors[0].code.includes("file-too-large")) {
            if (
              !err.includes(`File size should be less than ${maxFileSize} MB.`)
            ) {
              err.push(`File size should be less than ${maxFileSize} MB.`);
            }
          } else if (fileRejection.errors[0].code.includes("too-many-files")) {
            if (!err.includes(TooManyFiles)) {
              err.push(TooManyFiles);
            }
          } else {
            if (!err.includes(fileRejection.errors[0].message)) {
              err.push(fileRejection.errors[0].message);
            }
          }
        });

        const e = React.createElement("div", {
          dangerouslySetInnerHTML: { __html: err.join("<br/>") },
        });

        toast.error(e);

        return;
      }

      onFileChange(acceptedFiles);
    },
    [onFileChange],
  );

  const { getRootProps, getInputProps, open, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept: {
        "image/png": [],
        "image/jpeg": [],
        "image/jpg": [],
        "image/gif": [],
        "image/jfif": [],
      },
      maxFiles: fileLimit,
      multiple: false,
      noClick: true,
      noKeyboard: true,
      maxSize: maxFileSize * 1024 * 1024, // 5MB
    });

  const style = useMemo(
    () => ({
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isDragReject],
  );

  return (
    <div
      {...getRootProps({ style })}
      className={cn(
        "flex h-[450px] w-full flex-col items-center justify-center overflow-hidden rounded-[16px] border-1 text-center",
        isDragActive
          ? "border-solid border-[#3073D5] bg-[#E8F4FD] dark:border-[#3073D5] dark:bg-[#0B182B]"
          : "border-dashed border-[#E4E4E7] bg-[#FEFEFE] dark:border-[#32363D] dark:bg-[#22252A]",
      )}
    >
      <input {...getInputProps()} />
      {originalImage ? (
        <img src={originalImage} className="h-full w-full object-contain" />
      ) : (
        <div className="flex flex-col items-center justify-center gap-[14px] p-[10px]">
          <ImgIcon />
          <div className="inline-flex flex-col items-center justify-center gap-1.5 self-stretch">
            <div className="inline-flex items-center justify-start gap-1">
              <div className="justify-start text-[#11181C] font-['Inter'] text-sm leading-none font-normal">
                Drop your files here or
              </div>
              <div
                className="flex cursor-pointer items-center justify-center gap-2 rounded-xl"
                onClick={open}
              >
                <div className="justify-center font-['Inter'] text-sm leading-none font-semibold text-blue-600">
                  Click to upload
                </div>
              </div>
            </div>
            <div className="text-Contents-Tertiary justify-start self-stretch text-center font-['Inter'] text-xs leading-none font-normal text-[#64696B]">
              PNG, JPG or GIF (max. 5MB)
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
