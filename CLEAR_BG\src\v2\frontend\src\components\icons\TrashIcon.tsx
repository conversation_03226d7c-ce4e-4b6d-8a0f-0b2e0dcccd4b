import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "28",
  height = "28",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      width={size || width}
      height={size || height}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.76562 20.7651C7.76562 21.8651 8.66562 22.7651 9.76562 22.7651H17.7656C18.8656 22.7651 19.7656 21.8651 19.7656 20.7651V8.76514H7.76562V20.7651ZM9.76562 10.7651H17.7656V20.7651H9.76562V10.7651ZM17.2656 5.76514L16.2656 4.76514H11.2656L10.2656 5.76514H6.76562V7.76514H20.7656V5.76514H17.2656Z"
        fill={fill}
      />
    </svg>
  );
}
