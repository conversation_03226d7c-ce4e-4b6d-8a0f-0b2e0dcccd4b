{"name": "fullstack-app", "version": "1.0.0", "scripts": {"install-all": "npm install --prefix frontend && pip install -r backend/requirements.txt", "FE": "npm run dev --prefix frontend", "BE": "cd backend && uvicorn main:app --reload --port 9010", "build": "npm run build --prefix frontend", "start": "cd backend && uvicorn main:app --host 0.0.0.0 --port 9010"}, "author": "<PERSON><PERSON><PERSON>", "devDependencies": {"concurrently": "^9.2.0"}}