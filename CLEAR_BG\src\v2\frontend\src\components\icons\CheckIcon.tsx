import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "14",
  height = "14",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size || width}
      height={size || height}
      viewBox="0 0 16 16"
      fill="none"
    >
      <g clip-path="url(#clip0_3350_51803)">
        <path
          d="M6.00091 10.7999L3.20091 7.99994L2.26758 8.93328L6.00091 12.6666L14.0009 4.66661L13.0676 3.73328L6.00091 10.7999Z"
          fill={fill}
        />
      </g>
    </svg>
  );
}
