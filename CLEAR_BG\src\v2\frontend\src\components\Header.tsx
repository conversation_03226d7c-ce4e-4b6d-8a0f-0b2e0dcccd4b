import BackIcon from "./icons/BackIcon";
import SparkleIcon from "./icons/SparkleIcon";
import Diamond from "./icons/Diamond";
import { cn } from "../utils/cn";
import { useEffect, useMemo, useState } from "react";

export default function Header({
  className = "",
  setShowPricing,
}: {
  className?: string;
  setShowPricing: (value: boolean) => void;
}) {
  const [isProPlan, setIsProPlan] = useState<boolean>(false);

  useEffect(() => {
    const plan = window.AIPRO_USER?.subscription_type?.toString().toLowerCase();
    const isPro = !!plan && plan !== "basic";
    setIsProPlan(isPro);
  }, []);

  const startUrl = import.meta.env.VITE_START_URL || "https://start.ai-pro.org";
  const handleBackClick = () => {
    window.location.href = `${startUrl}/my-account`;
  };

  const showBackButton = useMemo(() => {
    const referrer = document.referrer;
    const isInvalidReferrer = !referrer || referrer === window.location.href;

    const isEmbedded = window.self !== window.top;

    return !isInvalidReferrer && !isEmbedded;
  }, []);

  return (
    <>
      <header
        className={cn(
          "flex h-[68px] min-h-[68px] items-center justify-between px-[20px]",
          showBackButton ? "justify-between" : "justify-end",
          className,
        )}
      >
        {showBackButton ? (
          <button
            onClick={handleBackClick}
            className="flex cursor-pointer items-center px-[8px] py-[11px] text-[14px] text-black-100 dark:text-white"
          >
            <BackIcon className="text-black-100 mr-[10px] dark:text-white" />
            Back to Dashboard
          </button>
        ) : null}

        {!isProPlan && (
          <>
            <button
              onClick={() => setShowPricing(true)}
              className="bg-gradient-blue hidden cursor-pointer items-center gap-[4px] rounded-3xl py-[11px] pr-[28.5px] pl-[22.5px] text-[14px] font-bold text-white md:flex dark:text-white"
            >
              <SparkleIcon size="18" className="text-white" />
              Go Pro
            </button>

            <button
              onClick={() => setShowPricing(true)}
              className="bg-gradient-blue flex cursor-pointer items-center rounded-3xl p-[10px] font-bold text-white md:hidden"
            >
              <Diamond size="16" />
            </button>
          </> 
        )}
      </header>
    </>
  );
}
