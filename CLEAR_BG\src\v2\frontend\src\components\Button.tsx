import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "../utils/cn";

// Define button variants using class-variance-authority
const buttonVariants = cva(
  // Base styles
  `cursor-pointer inline-flex items-center justify-center text-sm font-medium rounded-full transition-colors 
  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2 
  disabled:opacity-50 disabled:pointer-events-none`,
  {
    variants: {
      variant: {
        primary: "border bg-[#3073D5] text-white hover:bg-[#1E63C6]",
        secondary:
          "border bg-secondary border-secondary text-black hover:bg-secondary-hover",
        outline:
          "border border-neutral-300 bg-transparent hover:bg-neutral-100 dark:border-neutral-700 dark:hover:bg-neutral-800",
        ghost:
          "bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-50",
        gradient: "bg-gradient-blue text-white",
      },
      size: {
        xs: "h-5 px-[7px] text-xs",
        sm: "h-8.5 px-[8px] text-sm",
        md: "h-10 px-[22.5px] text-sm",
        lg: "h-14 px-[18px] text-lg",
        icon: "h-9 p-[10px]",
      },
      fullWidth: {
        true: "w-full",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
      fullWidth: false,
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  children?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, fullWidth, children, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, fullWidth, className }))}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    );
  },
);

Button.displayName = "Button";

export default Button;
